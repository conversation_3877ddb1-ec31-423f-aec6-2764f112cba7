import React from 'react';
import { render, screen } from '@testing-library/react';
import CustomExtendedVehicle from './CustomExtendedVehicle';

// Mock the utility functions
jest.mock('../utils/Util.js', () => ({
  getCellFace: jest.fn((item, className) => ({ ...item, className })),
  isValidSource: jest.fn(() => true),
}));

// Mock the BlinkBlock component
jest.mock('./elements/BlinkBlock', () => ({
  __esModule: true,
  default: ({ block }) => (
    <div data-testid="blink-block">
      {block.map((item, index) => (
        <div key={index} className={item.className}>
          {item.showInfo?.display_text}
        </div>
      ))}
    </div>
  ),
  checkBlinkInfo: jest.fn((info, base) => info || base),
}));

// Mock the CellBox component
jest.mock('./elements/CellBox', () => {
  return function CellBox({ children, ...props }) {
    return <div data-testid="cell-box" {...props}>{children}</div>;
  };
});

describe('CustomExtendedVehicle', () => {
  const mockProps = {
    is_deployment: 1,
    column_position: 'left',
    sourceDispPattern: 1,
    items: [
      {
        title: {
          display_text: '消防署',
          text_color: '#FFFFFF',
          background_color: '#FF0000'
        }
      },
      {
        car_name: {
          display_text: '救急車1',
          text_color: '#000000',
          background_color: '#FFFFFF'
        },
        town_name: {
          display_text: '中央区',
          text_color: '#000000',
          background_color: '#FFFFFF'
        },
        disaster_type: {
          display_text: '火災',
          text_color: '#000000',
          background_color: '#FFFFFF'
        },
        avm_dynamic_state: {
          display_text: '出動中',
          text_color: '#000000',
          background_color: '#FFFFFF'
        },
        deployment: {
          display_text: '配備',
          text_color: '#000000',
          background_color: '#FFFFFF'
        },
        lighting_setting: {
          lighting_status: 1,
          blink_interval: 500
        }
      }
    ]
  };

  test('renders without crashing', () => {
    render(<CustomExtendedVehicle {...mockProps} />);
  });

  test('renders title correctly', () => {
    render(<CustomExtendedVehicle {...mockProps} />);
    expect(screen.getByText('消防署')).toBeInTheDocument();
  });

  test('renders vehicle detail row correctly', () => {
    render(<CustomExtendedVehicle {...mockProps} />);
    expect(screen.getByTestId('blink-block')).toBeInTheDocument();
  });

  test('applies correct grid class for quad-split left column', () => {
    const { container } = render(<CustomExtendedVehicle {...mockProps} />);
    const gridElement = container.querySelector('.grid-cols-1.grid-rows-12');
    expect(gridElement).toBeInTheDocument();
  });

  test('applies correct grid class for quad-split right column', () => {
    const rightColumnProps = {
      ...mockProps,
      column_position: 'right'
    };
    const { container } = render(<CustomExtendedVehicle {...rightColumnProps} />);
    const gridElement = container.querySelector('.grid-cols-1.grid-rows-12');
    expect(gridElement).toBeInTheDocument();
  });

  test('applies correct grid class for single screen mode', () => {
    const singleScreenProps = {
      ...mockProps,
      sourceDispPattern: 0
    };
    const { container } = render(<CustomExtendedVehicle {...singleScreenProps} />);
    const gridElement = container.querySelector('.grid-cols-2.grid-rows-25');
    expect(gridElement).toBeInTheDocument();
  });

  test('handles empty items array', () => {
    const emptyProps = {
      ...mockProps,
      items: []
    };
    const { container } = render(<CustomExtendedVehicle {...emptyProps} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  test('handles missing items prop', () => {
    const { items, ...propsWithoutItems } = mockProps;
    const { container } = render(<CustomExtendedVehicle {...propsWithoutItems} />);
    expect(container.firstChild).toBeNull();
  });
});
