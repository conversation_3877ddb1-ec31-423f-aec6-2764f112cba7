<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="22130d8d-ff0a-466e-810a-2ff60f61d6ba" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/../../70. テスト/40.20250706/拡張車両コンテンツ52Row Full(配備なし).png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Client/src/components/Weather.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Client/src/components/Weather.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/system_setting.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/system_setting.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../70. テスト/40.20250706/テスト結果整理.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../70. テスト/40.20250706/実装結果.xlsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FindBugs-IDEA-Workspace">
    <toolWindowEditorPreview>false</toolWindowEditorPreview>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../../.." />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectId" id="2UR0toaowKWXEdceGxY7ErJkxtS" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;com.intellij.openapi.externalSystem.service.settings.ExternalSystemGroupConfigurable&quot;: &quot;ALL&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/11.Working/20.AutoControl/02.Output/90.Release用成果物/30.実装&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;build.tools&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;9aa1b03934893d7134a660af4204f2a9&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\typescript\\lib&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="22130d8d-ff0a-466e-810a-2ff60f61d6ba" name="Changes" comment="" />
      <created>1692887350340</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1692887350340</updated>
      <workItem from="1692887365037" duration="1229000" />
      <workItem from="1693014097978" duration="22020000" />
      <workItem from="1693099535741" duration="16813000" />
      <workItem from="1693634427217" duration="2692000" />
      <workItem from="1693702869466" duration="9423000" />
      <workItem from="1693715081399" duration="172000" />
      <workItem from="1693740355521" duration="779000" />
      <workItem from="1693745024560" duration="3486000" />
      <workItem from="1693835351775" duration="3359000" />
      <workItem from="1694259834993" duration="6326000" />
      <workItem from="1694267103816" duration="381000" />
      <workItem from="1694271079287" duration="357000" />
      <workItem from="1694305343264" duration="2478000" />
      <workItem from="1696682141002" duration="608000" />
      <workItem from="1697326189142" duration="732000" />
      <workItem from="1750470396155" duration="3881000" />
      <workItem from="1750489144630" duration="9027000" />
      <workItem from="1751699289637" duration="13583000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/external/monitor/pn_hs/SingleMonitorControl.java</url>
          <line>408</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/configuration/websocket/MonitorHandler.java</url>
          <line>47</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/configuration/websocket/MonitorHandler.java</url>
          <line>23</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/configuration/websocket/WebSocketStompConfig.java</url>
          <line>64</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/configuration/websocket/WebSocketStompConfig.java</url>
          <line>23</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>634</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>649</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>598</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>322</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/api/ControlMonitorApiController.java</url>
          <line>57</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>216</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>137</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>340</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>221</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>239</line>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>254</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/contents/service/SystemService.java</url>
          <line>230</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>